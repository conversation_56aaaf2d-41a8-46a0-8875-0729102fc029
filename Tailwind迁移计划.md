# Tailwind CSS 移除迁移计划

## 📋 迁移策略

### 第一阶段：准备工作 (0.5天)
1. 创建Ant Design样式工具函数
2. 建立CSS Modules文件结构
3. 配置构建工具

### 第二阶段：核心组件迁移 (1天)
1. 布局组件 (MainLayout, Sidebar, Header)
2. 通用组件 (Card, Button扩展, Form扩展)
3. 数据展示组件 (Table, List, Statistics)

### 第三阶段：业务组件迁移 (0.5天)
1. 生产管理相关组件
2. 销售管理相关组件
3. 库存管理相关组件

## 🛠️ 迁移工具函数

### Ant Design样式增强工具
```typescript
// src/utils/styles/antdHelpers.ts
export const styleHelpers = {
  // 间距工具
  spacing: {
    xs: 4,
    sm: 8, 
    md: 16,
    lg: 24,
    xl: 32
  },
  
  // 阴影工具
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
  },
  
  // 圆角工具
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12
  }
}