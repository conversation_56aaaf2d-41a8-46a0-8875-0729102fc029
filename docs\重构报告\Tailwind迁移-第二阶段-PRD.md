# Tailwind迁移 - 第二阶段PRD文档

**文档版本**: v1.0  
**创建日期**: 2025-01-24  
**阶段名称**: 核心组件迁移阶段  
**预估工时**: 8小时 (1个工作日)  
**负责人**: [待分配]  
**状态**: 待开始  
**前置条件**: 第一阶段完成  

---

## 📋 阶段概述

### 目标
将项目中的核心组件从Tailwind CSS迁移到纯Ant Design + CSS Modules方案，确保样式功能完全一致。

### 范围
- 布局组件 (MainLayout, Sidebar, Header)
- 通用组件 (Card, Button扩展, Form扩展)
- 数据展示组件 (Table, List, Statistics)

### 成功标准
- [ ] 所有核心组件完成Tailwind类名移除
- [ ] 组件样式效果与迁移前完全一致
- [ ] 组件功能正常，无回归问题
- [ ] 代码质量符合团队规范

---

## 🎯 详细任务清单

### 任务1: 布局组件迁移
**预估工时**: 3小时  
**优先级**: P0 (最高)  
**依赖**: 第一阶段完成  

#### 子任务清单
- [ ] **1.1** 迁移MainLayout组件
  - 文件路径: `src/components/layout/MainLayout.tsx`
  - 移除所有Tailwind类名
  - 使用Ant Design Layout组件和内联样式
- [ ] **1.2** 迁移Sidebar组件
  - 文件路径: `src/components/layout/Sidebar.tsx`
  - 替换导航样式为Ant Design Menu
  - 创建对应的CSS Modules文件
- [ ] **1.3** 迁移Header组件
  - 文件路径: `src/components/layout/Header.tsx`
  - 使用Ant Design Flex和Space组件
  - 保持响应式布局效果
- [ ] **1.4** 创建布局相关CSS Modules
  - 创建 `src/components/layout/Layout.module.css`
  - 定义特殊布局样式

#### 技术实施细节
```typescript
// MainLayout迁移示例
// 迁移前
<Layout className="min-h-screen">
  <Sider className="bg-white shadow-sm">
    <div className="p-4 border-b border-gray-200">

// 迁移后
<Layout style={{ minHeight: '100vh' }}>
  <Layout.Sider style={{ 
    background: '#fff',
    boxShadow: styleHelpers.shadows.sm
  }}>
    <div style={{ 
      padding: styleHelpers.spacing.md,
      borderBottom: '1px solid #f0f0f0'
    }}>
```

#### 验收标准
- 布局组件视觉效果与迁移前一致
- 响应式布局功能正常
- 无Tailwind类名残留
- 组件能够正常渲染和交互

#### 风险评估
- **风险**: 复杂布局样式难以用Ant Design组件完全替代
- **缓解**: 使用CSS Modules补充特殊样式需求

---

### 任务2: 通用组件迁移
**预估工时**: 2.5小时  
**优先级**: P0 (最高)  
**依赖**: 任务1完成  

#### 子任务清单
- [ ] **2.1** 迁移自定义Card组件
  - 文件路径: `src/components/common/Card.tsx`
  - 基于Ant Design Card组件扩展
  - 保持原有的hover效果和动画
- [ ] **2.2** 迁移Button扩展组件
  - 文件路径: `src/components/common/Button.tsx`
  - 使用Ant Design Button的自定义样式
  - 保持所有变体样式
- [ ] **2.3** 迁移Form扩展组件
  - 文件路径: `src/components/common/FormComponents.tsx`
  - 基于Ant Design Form组件
  - 保持自定义验证样式
- [ ] **2.4** 创建通用组件CSS Modules
  - 创建 `src/components/common/Common.module.css`
  - 定义组件特有样式

#### 技术实施细节
```typescript
// Card组件迁移示例
import { Card as AntCard } from 'antd'
import styles from './Common.module.css'

const Card: React.FC<CardProps> = ({ 
  children, 
  hoverable = true, 
  ...props 
}) => {
  return (
    <AntCard
      {...props}
      className={`${hoverable ? styles.cardHover : ''} ${props.className || ''}`}
      style={{
        borderRadius: styleHelpers.borderRadius.md,
        boxShadow: styleHelpers.shadows.sm,
        ...props.style
      }}
    >
      {children}
    </AntCard>
  )
}
```

#### 验收标准
- 所有通用组件功能完整
- 组件API保持向后兼容
- 样式效果与原版一致
- 组件性能无明显下降

#### 风险评估
- **风险**: 组件API变更可能影响使用方
- **缓解**: 保持原有API不变，内部实现迁移

---

### 任务3: 数据展示组件迁移
**预估工时**: 2.5小时  
**优先级**: P1 (高)  
**依赖**: 任务2完成  

#### 子任务清单
- [ ] **3.1** 迁移自定义Table组件
  - 文件路径: `src/components/common/Table.tsx`
  - 基于Ant Design Table扩展
  - 保持自定义列配置和样式
- [ ] **3.2** 迁移List组件
  - 文件路径: `src/components/common/List.tsx`
  - 使用Ant Design List组件
  - 保持原有的列表项样式
- [ ] **3.3** 迁移Statistics组件
  - 文件路径: `src/components/common/Statistics.tsx`
  - 基于Ant Design Statistic组件
  - 保持数据可视化效果
- [ ] **3.4** 迁移数据展示相关样式
  - 更新相关CSS Modules文件
  - 确保数据展示的视觉一致性

#### 技术实施细节
```typescript
// Statistics组件迁移示例
import { Statistic, Card, Flex } from 'antd'
import { ArrowUpOutlined } from '@ant-design/icons'

const StatisticsCard: React.FC<StatisticsProps> = ({
  title,
  value,
  trend,
  trendValue
}) => {
  return (
    <Card style={{ 
      borderRadius: styleHelpers.borderRadius.md,
      boxShadow: styleHelpers.shadows.sm 
    }}>
      <Flex justify="space-between" align="center">
        <Statistic
          title={title}
          value={value}
          valueStyle={{ 
            fontSize: '24px',
            fontWeight: 600,
            color: '#262626'
          }}
        />
        <Flex align="center" style={{ 
          color: trend === 'up' ? '#52c41a' : '#ff4d4f' 
        }}>
          <ArrowUpOutlined style={{ marginRight: 4 }} />
          <span>{trendValue}</span>
        </Flex>
      </Flex>
    </Card>
  )
}
```

#### 验收标准
- 数据展示组件功能完整
- 表格、列表等组件交互正常
- 统计数据展示效果良好
- 响应式设计保持一致

#### 风险评估
- **风险**: 复杂的表格自定义样式可能难以迁移
- **缓解**: 使用Ant Design的自定义渲染功能

---

## 📊 任务依赖关系图

```
第一阶段完成
    ↓
任务1: 布局组件迁移
    ↓
任务2: 通用组件迁移
    ↓
任务3: 数据展示组件迁移
```

## ⏰ 时间安排

| 任务 | 开始时间 | 结束时间 | 工时 |
|------|----------|----------|------|
| 任务1 | 09:00 | 12:00 | 3h |
| 任务2 | 13:00 | 15:30 | 2.5h |
| 任务3 | 15:30 | 18:00 | 2.5h |

## 🔍 组件迁移检查清单

### 迁移前检查
- [ ] 记录组件当前的视觉效果（截图）
- [ ] 记录组件的所有功能特性
- [ ] 识别所有使用的Tailwind类名
- [ ] 确认组件的API接口

### 迁移过程检查
- [ ] 逐步替换Tailwind类名
- [ ] 保持组件API不变
- [ ] 测试组件功能完整性
- [ ] 验证样式效果一致性

### 迁移后检查
- [ ] 视觉效果对比验证
- [ ] 功能回归测试
- [ ] 性能影响评估
- [ ] 代码质量检查

## 🧪 测试策略

### 单元测试
- [ ] 为每个迁移的组件编写/更新单元测试
- [ ] 测试组件的渲染和基本功能
- [ ] 测试组件的props传递

### 集成测试
- [ ] 测试组件在页面中的集成效果
- [ ] 测试组件间的交互功能
- [ ] 测试响应式布局

### 视觉回归测试
- [ ] 对比迁移前后的视觉效果
- [ ] 确保在不同屏幕尺寸下的一致性
- [ ] 验证动画和交互效果

## 🚨 风险管控

### 高风险项
1. **样式效果不一致**: 迁移后视觉效果与原版不符
   - **监控**: 每个组件迁移后立即进行视觉对比
   - **应对**: 使用CSS Modules补充差异样式

2. **组件功能回归**: 迁移过程中破坏原有功能
   - **监控**: 每个组件迁移后运行完整测试
   - **应对**: 保持原有API不变，仅修改内部实现

### 中风险项
1. **性能影响**: 新的样式方案可能影响性能
   - **监控**: 使用性能分析工具对比前后差异
   - **应对**: 优化样式实现，减少不必要的重渲染

2. **开发体验下降**: 新的样式写法可能影响开发效率
   - **监控**: 收集开发者反馈
   - **应对**: 完善工具函数，提供更好的开发体验

## 📋 交付物清单

### 迁移的组件文件
- [ ] `src/components/layout/MainLayout.tsx`
- [ ] `src/components/layout/Sidebar.tsx`
- [ ] `src/components/layout/Header.tsx`
- [ ] `src/components/common/Card.tsx`
- [ ] `src/components/common/Button.tsx`
- [ ] `src/components/common/FormComponents.tsx`
- [ ] `src/components/common/Table.tsx`
- [ ] `src/components/common/List.tsx`
- [ ] `src/components/common/Statistics.tsx`

### 新增CSS Modules文件
- [ ] `src/components/layout/Layout.module.css`
- [ ] `src/components/common/Common.module.css`

### 更新的测试文件
- [ ] 各组件对应的测试文件更新
- [ ] 新增视觉回归测试用例

### 文档更新
- [ ] 组件使用文档更新
- [ ] 样式迁移指南更新
- [ ] 开发规范文档更新

## 📈 质量指标

### 代码质量
- [ ] ESLint检查通过率: 100%
- [ ] TypeScript类型检查通过率: 100%
- [ ] 单元测试覆盖率: ≥ 85%

### 功能质量
- [ ] 组件功能回归测试通过率: 100%
- [ ] 视觉效果一致性: ≥ 95%
- [ ] 响应式布局正确性: 100%

### 性能指标
- [ ] 组件渲染时间变化: ≤ +10%
- [ ] 页面加载时间变化: ≤ +5%
- [ ] 内存使用变化: ≤ +5%

---

**阶段完成标志**: 所有核心组件成功迁移，功能和样式效果与迁移前保持一致，所有测试通过。