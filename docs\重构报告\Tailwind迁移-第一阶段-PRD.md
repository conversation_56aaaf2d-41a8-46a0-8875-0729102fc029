# Tailwind迁移 - 第一阶段PRD文档

**文档版本**: v1.0  
**创建日期**: 2025-01-24  
**阶段名称**: 准备工作阶段  
**预估工时**: 4小时 (0.5个工作日)  
**负责人**: [待分配]  
**状态**: 待开始  

---

## 📋 阶段概述

### 目标
完成Tailwind CSS移除的前期准备工作，建立基于Ant Design的样式工具体系，为后续组件迁移奠定基础。

### 范围
- 移除Tailwind CSS相关配置和依赖
- 创建Ant Design样式增强工具函数
- 建立CSS Modules文件结构
- 配置构建工具支持新的样式方案

### 成功标准
- [ ] Tailwind CSS完全移除，项目能正常构建
- [ ] 样式工具函数创建完成并通过测试
- [ ] CSS Modules配置生效
- [ ] 构建工具配置更新完成

---

## 🎯 详细任务清单

### 任务1: 移除Tailwind CSS配置和依赖
**预估工时**: 1小时  
**优先级**: P0 (最高)  
**依赖**: 无  

#### 子任务清单
- [ ] **1.1** 移除package.json中的Tailwind相关依赖
  - 移除 `tailwindcss`
  - 移除 `autoprefixer` 
  - 移除 `postcss`
- [ ] **1.2** 删除Tailwind配置文件
  - 删除 `tailwind.config.js`
  - 删除 `postcss.config.js`
- [ ] **1.3** 更新全局样式文件
  - 修改 `src/app/globals.css`
  - 移除 `@tailwind` 指令
- [ ] **1.4** 验证项目构建
  - 运行 `npm run build`
  - 确保无Tailwind相关错误

#### 技术实施细节
```bash
# 移除依赖
npm uninstall tailwindcss autoprefixer postcss

# 删除配置文件
rm tailwind.config.js
rm postcss.config.js
```

#### 验收标准
- 项目能够正常构建，无Tailwind相关错误
- package.json中不包含Tailwind相关依赖
- 配置文件已完全删除

#### 风险评估
- **风险**: 可能存在遗漏的Tailwind类名导致样式丢失
- **缓解**: 在后续任务中逐步替换，暂时保留原有样式

---

### 任务2: 创建Ant Design样式增强工具
**预估工时**: 1.5小时  
**优先级**: P0 (最高)  
**依赖**: 任务1完成  

#### 子任务清单
- [ ] **2.1** 创建样式工具函数文件
  - 创建 `src/utils/styles/antdHelpers.ts`
  - 实现间距、阴影、圆角等工具函数
- [ ] **2.2** 创建主题配置文件
  - 创建 `src/styles/theme.ts`
  - 定义统一的设计token
- [ ] **2.3** 创建样式常量文件
  - 创建 `src/styles/constants.ts`
  - 定义常用的样式常量
- [ ] **2.4** 编写工具函数测试
  - 创建 `src/utils/styles/__tests__/antdHelpers.test.ts`
  - 覆盖主要工具函数

#### 技术实施细节
```typescript
// src/utils/styles/antdHelpers.ts
export const styleHelpers = {
  spacing: {
    xs: 4, sm: 8, md: 16, lg: 24, xl: 32
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
  },
  borderRadius: {
    sm: 4, md: 8, lg: 12
  },
  // 生成内联样式的工具函数
  createSpacing: (size: keyof typeof styleHelpers.spacing) => ({
    padding: styleHelpers.spacing[size]
  }),
  createShadow: (size: keyof typeof styleHelpers.shadows) => ({
    boxShadow: styleHelpers.shadows[size]
  })
}
```

#### 验收标准
- 样式工具函数文件创建完成
- 所有工具函数通过单元测试
- 主题配置能够正确应用到Ant Design组件
- TypeScript类型检查通过

#### 风险评估
- **风险**: 工具函数设计不够灵活，无法满足所有场景
- **缓解**: 采用渐进式设计，后续根据实际使用情况调整

---

### 任务3: 建立CSS Modules文件结构
**预估工时**: 1小时  
**优先级**: P1 (高)  
**依赖**: 任务2完成  

#### 子任务清单
- [ ] **3.1** 创建CSS Modules目录结构
  - 创建 `src/styles/modules/` 目录
  - 创建组件级样式目录结构
- [ ] **3.2** 创建通用CSS Modules文件
  - 创建 `src/styles/modules/common.module.css`
  - 定义通用的CSS类
- [ ] **3.3** 创建布局相关CSS Modules
  - 创建 `src/styles/modules/layout.module.css`
  - 定义布局相关样式
- [ ] **3.4** 创建动画CSS Modules
  - 创建 `src/styles/modules/animations.module.css`
  - 定义常用动画效果

#### 技术实施细节
```css
/* src/styles/modules/common.module.css */
.card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.cardHover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.flexCenter {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flexBetween {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
```

#### 验收标准
- CSS Modules目录结构创建完成
- 通用样式类定义完整
- 样式文件能够正确导入和使用
- 样式类命名规范统一

#### 风险评估
- **风险**: CSS Modules类名冲突
- **缓解**: 采用BEM命名规范，确保类名唯一性

---

### 任务4: 配置构建工具
**预估工时**: 0.5小时  
**优先级**: P1 (高)  
**依赖**: 任务1-3完成  

#### 子任务清单
- [ ] **4.1** 更新Next.js配置
  - 修改 `next.config.js`
  - 启用CSS Modules支持
  - 移除PostCSS相关配置
- [ ] **4.2** 更新TypeScript配置
  - 修改 `tsconfig.json`
  - 添加CSS Modules类型支持
- [ ] **4.3** 创建CSS Modules类型声明
  - 创建 `src/types/css-modules.d.ts`
  - 定义CSS Modules类型
- [ ] **4.4** 验证构建配置
  - 运行完整构建流程
  - 确保所有配置生效

#### 技术实施细节
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ['antd'],
  compiler: {
    styledComponents: true,
  },
  reactStrictMode: false,
  
  // 启用CSS Modules支持
  cssModules: true,
  cssLoaderOptions: {
    modules: {
      localIdentName: '[name]__[local]___[hash:base64:5]',
    },
  },
}

module.exports = nextConfig
```

```typescript
// src/types/css-modules.d.ts
declare module '*.module.css' {
  const classes: { [key: string]: string }
  export default classes
}

declare module '*.module.scss' {
  const classes: { [key: string]: string }
  export default classes
}
```

#### 验收标准
- Next.js配置更新完成
- CSS Modules能够正确加载和使用
- TypeScript类型检查通过
- 构建流程无错误

#### 风险评估
- **风险**: 构建配置冲突导致项目无法启动
- **缓解**: 分步骤验证配置，及时回滚问题配置

---

## 📊 任务依赖关系图

```
任务1: 移除Tailwind配置
    ↓
任务2: 创建样式工具函数
    ↓
任务3: 建立CSS Modules结构
    ↓
任务4: 配置构建工具
```

## ⏰ 时间安排

| 任务 | 开始时间 | 结束时间 | 工时 |
|------|----------|----------|------|
| 任务1 | 09:00 | 10:00 | 1h |
| 任务2 | 10:00 | 11:30 | 1.5h |
| 任务3 | 11:30 | 12:30 | 1h |
| 任务4 | 14:00 | 14:30 | 0.5h |

## 🔍 质量检查清单

### 代码质量
- [ ] 所有新增文件通过ESLint检查
- [ ] TypeScript类型检查无错误
- [ ] 单元测试覆盖率 ≥ 80%

### 功能验证
- [ ] 项目能够正常启动
- [ ] 样式工具函数功能正常
- [ ] CSS Modules能够正确应用

### 文档更新
- [ ] 更新README.md中的样式使用说明
- [ ] 创建样式工具函数使用文档
- [ ] 更新开发规范文档

## 🚨 风险管控

### 高风险项
1. **Tailwind类名遗漏**: 可能导致样式丢失
   - **监控**: 运行时样式检查
   - **应对**: 建立样式映射表，逐步替换

2. **构建配置冲突**: 可能导致项目无法启动
   - **监控**: 每次配置修改后立即验证
   - **应对**: 保留配置备份，快速回滚

### 中风险项
1. **工具函数设计不当**: 可能影响后续开发效率
   - **监控**: 收集开发者使用反馈
   - **应对**: 快速迭代优化工具函数

## 📋 交付物清单

### 代码文件
- [ ] `src/utils/styles/antdHelpers.ts` - 样式工具函数
- [ ] `src/styles/theme.ts` - 主题配置
- [ ] `src/styles/constants.ts` - 样式常量
- [ ] `src/styles/modules/common.module.css` - 通用样式
- [ ] `src/styles/modules/layout.module.css` - 布局样式
- [ ] `src/styles/modules/animations.module.css` - 动画样式
- [ ] `src/types/css-modules.d.ts` - 类型声明

### 配置文件
- [ ] `next.config.js` - 更新后的Next.js配置
- [ ] `tsconfig.json` - 更新后的TypeScript配置
- [ ] `src/app/globals.css` - 更新后的全局样式

### 测试文件
- [ ] `src/utils/styles/__tests__/antdHelpers.test.ts` - 工具函数测试

### 文档文件
- [ ] `docs/开发指南/样式工具函数使用指南.md` - 使用文档
- [ ] 更新后的 `README.md`

---

**阶段完成标志**: 所有任务清单项目勾选完成，质量检查通过，交付物全部就绪。